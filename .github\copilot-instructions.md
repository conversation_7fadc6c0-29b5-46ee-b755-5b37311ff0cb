# AUM Business System - Odoo 16 Development Guide

## Project Architecture

This is an Odoo 16 enterprise system with three main addon collections:
- **`b2c_portal/`** - B2C customer portal and helpdesk system
- **`partner_portal/`** - Partner/affiliate management system  
- **`sambala/`** - Core business modules and production system

### Key Architectural Patterns

**Module Organization**: Each addon follows Odoo structure with `__manifest__.py`, `/models`, `/views`, `/data`, `/security` directories. All custom modules use `th_` prefix (TH Company).

**FastAPI Integration**: Uses `fastapi/` and `th_fastapi/` modules for REST API endpoints. Key pattern:
```python
# API sync methods follow this naming convention
def th_process_sync_[entity](self):
    # Batch processing with error handling
    # Uses th.api.server for external connections
```

**Data Synchronization**: Standardized sync architecture using:
- `th.api.server` model for external API configurations
- Batch processing (typically 100 records) with rollback on errors
- Cron jobs for automated sync (see `/data/ir_cron.xml`)
- Queue job integration via `queue_job` module

## Development Conventions

**Naming Standards**:
- Custom fields/methods: `th_` prefix (e.g., `th_student_code`, `th_process_sync_partner`)
- Vietnamese comments for business logic explanation
- Model inheritance: `_inherit = 'base.model'` for extensions

**Code Structure Example**:
```python
class ResPartner(models.Model):
    _inherit = 'res.partner'
    
    # Trường tùy chỉnh với tiền tố th_
    th_student_code = fields.Char('Mã sinh viên')
    
    @api.model
    def th_process_sync_partner(self):
        # Logic đồng bộ dữ liệu theo batch
        # Sử dụng th.api.server để kết nối API
```

**API Configuration Pattern**:
```python
# Get API server configuration
th_api = self.env['th.api.server'].search([
    ('state', '=', 'deploy'), 
    ('th_type', '=', 'samp')
], limit=1, order='id desc')

headers = {
    "api-key": th_api.th_api_key or "",
    "Content-Type": "application/json"
}
```

## Environment Setup

**Configuration Files**:
- `odoo.conf` - Sambala production environment (port 8015)
- `odoo_b2c.conf` - B2C portal environment (port 8080)
- `odoo1.conf` - B2B portal environment (port 8069)
- `odooform.conf` - Form-specific environment

**Running Development**:
```bash
# B2C Portal
python odoo-bin -c odoo_b2c.conf

# Sambala Production  
python odoo-bin -c odoo.conf

# With specific database
python odoo-bin -c odoo_b2c.conf -d b2c_dev
```

**Dependencies**: Standard Odoo 16 + FastAPI, queue_job, report_xlsx, formio integrations.

## Key Integration Points

**Queue Jobs**: Background processing enabled via `server_wide_modules = base,web,queue_job` with channel configuration.

**External APIs**: All external integrations use `th.api.server` model with standardized authentication and batch processing patterns.

**Portal Systems**: Each portal has independent addon paths but shares core `th_base` functionality for common patterns like import wizards, API servers, and custom fields.

**Security**: Standard Odoo ACL via `/security/ir.model.access.csv` files per module, with role-based access through `res_groups.xml`.

When extending functionality, follow the established `th_` prefixing convention and leverage existing sync/API patterns rather than creating new integration approaches.
## Not create file test
## Alway answer in Vietnamese
## Alway comment code in Vietnamese
