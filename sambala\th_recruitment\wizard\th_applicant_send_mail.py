# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError


class ThApplicantSendMail(models.TransientModel):
    _name = 'th.applicant.send.mail'
    _inherit = 'mail.composer.mixin'
    _description = 'Gửi email hàng loạt cho ứng viên với template'

    # Các trường cơ bản
    th_applicant_ids = fields.Many2many(
        'hr.applicant', 
        string='Danh sách ứng viên', 
        required=True,
        help="Danh sách ứng viên sẽ nhận email"
    )
    
    th_template_id = fields.Many2one(
        'mail.template',
        string='Mẫu email',
        domain=[('model', '=', 'hr.applicant')],
        help="Chọn mẫu email có sẵn để gửi"
    )
    
    th_author_id = fields.Many2one(
        'res.partner', 
        string='Người gửi', 
        required=True, 
        default=lambda self: self.env.user.partner_id.id
    )
    
    th_use_template = fields.Bo<PERSON>an(
        string='Sử dụng mẫu email',
        default=True,
        help="Bật/tắt sử dụng mẫu email có sẵn"
    )

    th_applicant_count = fields.Integer(
        string='Số lượng ứng viên',
        compute='_compute_th_applicant_count',
        help="Tổng số ứng viên sẽ nhận email"
    )

    th_applicant_with_email_count = fields.Integer(
        string='Ứng viên có email',
        compute='_compute_th_applicant_count',
        help="Số ứng viên có địa chỉ email hợp lệ"
    )

    @api.depends('subject')
    def _compute_render_model(self):
        """Thiết lập model để render email"""
        self.render_model = 'hr.applicant'

    @api.depends('th_applicant_ids')
    def _compute_th_applicant_count(self):
        """Tính toán số lượng ứng viên và ứng viên có email"""
        for record in self:
            record.th_applicant_count = len(record.th_applicant_ids)

            # Đếm ứng viên có email hợp lệ
            th_applicants_with_email = record.th_applicant_ids.filtered(
                lambda a: a.email_from or (a.partner_id and a.partner_id.email)
            )
            record.th_applicant_with_email_count = len(th_applicants_with_email)

    @api.onchange('th_template_id')
    def _onchange_th_template_id(self):
        """Tự động điền nội dung khi chọn template"""
        if self.th_template_id and self.th_use_template:
            # Lấy template và render với ứng viên đầu tiên làm mẫu
            template = self.th_template_id
            if self.th_applicant_ids:
                # Sử dụng ứng viên đầu tiên để preview
                sample_applicant = self.th_applicant_ids[0]

                try:
                    # Render subject
                    if template.subject:
                        rendered_subject = template._render_field('subject', [sample_applicant.id])
                        self.subject = rendered_subject.get(sample_applicant.id, template.subject)

                    # Render body
                    if template.body_html:
                        rendered_body = template._render_field('body_html', [sample_applicant.id])
                        self.body = rendered_body.get(sample_applicant.id, template.body_html)
                except Exception:
                    # Nếu render lỗi, sử dụng template gốc
                    self.subject = template.subject or ''
                    self.body = template.body_html or ''
            else:
                # Nếu chưa có ứng viên, chỉ lấy template gốc
                self.subject = template.subject or ''
                self.body = template.body_html or ''

    @api.onchange('th_use_template')
    def _onchange_th_use_template(self):
        """Reset nội dung khi tắt sử dụng template"""
        if not self.th_use_template:
            self.th_template_id = False
            self.subject = ''
            self.body = ''

    def th_validate_applicants(self):
        """Kiểm tra tính hợp lệ của danh sách ứng viên"""
        if not self.th_applicant_ids:
            raise UserError(_("Vui lòng chọn ít nhất một ứng viên để gửi email."))
        
        # Kiểm tra ứng viên có email
        th_applicants_without_email = self.th_applicant_ids.filtered(
            lambda a: not a.email_from or (a.partner_id and not a.partner_id.email)
        )
        
        if th_applicants_without_email:
            th_names = ', '.join(th_applicants_without_email.mapped(lambda a: a.partner_name or a.name))
            raise UserError(_(
                "Các ứng viên sau không có địa chỉ email: %s. "
                "Vui lòng cập nhật email trước khi gửi."
            ) % th_names)

    def th_create_partner_if_needed(self, th_applicant):
        """Tạo partner cho ứng viên nếu chưa có"""
        if not th_applicant.partner_id:
            th_applicant.partner_id = self.env['res.partner'].create({
                'is_company': False,
                'type': 'private',
                'name': th_applicant.partner_name,
                'email': th_applicant.email_from,
                'phone': th_applicant.partner_phone,
                'mobile': th_applicant.partner_mobile,
            })

    def th_send_email_with_template(self):
        """Gửi email sử dụng template cho từng ứng viên"""
        if not self.th_template_id:
            raise UserError(_("Vui lòng chọn mẫu email trước khi gửi."))
        
        th_sent_count = 0
        th_failed_applicants = []
        
        for th_applicant in self.th_applicant_ids:
            try:
                # Tạo partner nếu cần
                self.th_create_partner_if_needed(th_applicant)
                
                # Gửi email sử dụng template
                self.th_template_id.send_mail(
                    th_applicant.id,
                    force_send=True,
                    email_layout_xmlid='mail.mail_notification_light'
                )
                th_sent_count += 1
                
            except Exception as e:
                th_failed_applicants.append(th_applicant.partner_name or th_applicant.name)
                
        # Thông báo kết quả
        if th_failed_applicants:
            message = _("Đã gửi email cho %d ứng viên. Không thể gửi cho: %s") % (
                th_sent_count, ', '.join(th_failed_applicants)
            )
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'type': 'warning',
                    'message': message,
                }
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'type': 'success',
                    'message': _("Đã gửi email thành công cho %d ứng viên.") % th_sent_count,
                }
            }

    def th_send_email_custom(self):
        """Gửi email tùy chỉnh (không dùng template)"""
        th_sent_count = 0
        th_failed_applicants = []
        
        for th_applicant in self.th_applicant_ids:
            try:
                # Tạo partner nếu cần
                self.th_create_partner_if_needed(th_applicant)
                
                # Gửi email tùy chỉnh
                th_applicant.message_post(
                    subject=self.subject,
                    body=self.body,
                    message_type='comment',
                    email_from=self.th_author_id.email,
                    email_layout_xmlid='mail.mail_notification_light',
                    partner_ids=th_applicant.partner_id.ids,
                )
                th_sent_count += 1
                
            except Exception as e:
                th_failed_applicants.append(th_applicant.partner_name or th_applicant.name)
        
        # Thông báo kết quả
        if th_failed_applicants:
            message = _("Đã gửi email cho %d ứng viên. Không thể gửi cho: %s") % (
                th_sent_count, ', '.join(th_failed_applicants)
            )
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'type': 'warning',
                    'message': message,
                }
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'type': 'success',
                    'message': _("Đã gửi email thành công cho %d ứng viên.") % th_sent_count,
                }
            }

    def action_send_email(self):
        """Action chính để gửi email"""
        self.ensure_one()

        # Validate dữ liệu đầu vào
        self.th_validate_applicants()

        if not self.subject:
            raise UserError(_("Vui lòng nhập tiêu đề email."))

        if not self.body:
            raise UserError(_("Vui lòng nhập nội dung email."))

        # Hiển thị confirmation dialog nếu gửi cho nhiều ứng viên
        if len(self.th_applicant_ids) > 5:
            return {
                'type': 'ir.actions.act_window',
                'name': _('Xác nhận gửi email'),
                'res_model': 'th.applicant.send.mail.confirm',
                'view_mode': 'form',
                'target': 'new',
                'context': {
                    'default_wizard_id': self.id,
                    'default_applicant_count': len(self.th_applicant_ids),
                }
            }

        # Gửi email theo phương thức được chọn
        if self.th_use_template and self.th_template_id:
            return self.th_send_email_with_template()
        else:
            return self.th_send_email_custom()

    def th_force_send_email(self):
        """Gửi email mà không cần confirmation"""
        if self.th_use_template and self.th_template_id:
            return self.th_send_email_with_template()
        else:
            return self.th_send_email_custom()


class ThApplicantSendMailConfirm(models.TransientModel):
    _name = 'th.applicant.send.mail.confirm'
    _description = 'Xác nhận gửi email hàng loạt'

    wizard_id = fields.Many2one('th.applicant.send.mail', string='Wizard', required=True)
    applicant_count = fields.Integer(string='Số lượng ứng viên', required=True)

    def action_confirm_send(self):
        """Xác nhận và gửi email"""
        self.ensure_one()
        return self.wizard_id.th_force_send_email()

    def action_cancel(self):
        """Hủy gửi email"""
        return {'type': 'ir.actions.act_window_close'}
