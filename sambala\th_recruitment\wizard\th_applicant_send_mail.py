# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError


class ThApplicantSendMail(models.TransientModel):
    _name = 'th.applicant.send.mail'
    _inherit = 'mail.composer.mixin'
    _description = 'Gửi email hàng loạt cho ứng viên theo giai đoạn'

    # Các trường cơ bản
    th_applicant_ids = fields.Many2many(
        'hr.applicant',
        string='Danh sách ứng viên',
        required=True,
        help="Danh sách ứng viên sẽ nhận email"
    )

    th_author_id = fields.Many2one(
        'res.partner',
        string='Người gửi',
        required=True,
        default=lambda self: self.env.user.partner_id.id
    )

    # Thống kê và thông tin
    th_applicant_count = fields.Integer(
        string='Tổng số ứng viên',
        compute='_compute_th_stage_groups',
        help="Tổng số ứng viên đư<PERSON> chọ<PERSON>"
    )

    th_applicant_with_email_count = fields.Integer(
        string='Ứng viên có email',
        compute='_compute_th_stage_groups',
        help="Số ứng viên có địa chỉ email hợp lệ"
    )

    th_stage_group_info = fields.Html(
        string='Thông tin nhóm giai đoạn',
        compute='_compute_th_stage_groups',
        help="Chi tiết về các nhóm ứng viên theo giai đoạn"
    )

    @api.depends('subject')
    def _compute_render_model(self):
        """Thiết lập model để render email"""
        self.render_model = 'hr.applicant'

    @api.depends('th_applicant_ids')
    def _compute_th_stage_groups(self):
        """Tính toán thông tin nhóm ứng viên theo giai đoạn"""
        for record in self:
            if not record.th_applicant_ids:
                record.th_applicant_count = 0
                record.th_applicant_with_email_count = 0
                record.th_stage_group_info = ''
                continue

            # Tính tổng số ứng viên
            record.th_applicant_count = len(record.th_applicant_ids)

            # Đếm ứng viên có email hợp lệ
            th_applicants_with_email = record.th_applicant_ids.filtered(
                lambda a: record.th_get_applicant_email(a)
            )
            record.th_applicant_with_email_count = len(th_applicants_with_email)

            # Nhóm ứng viên theo stage_id
            th_stage_groups = {}
            for applicant in record.th_applicant_ids:
                stage = applicant.stage_id
                if stage not in th_stage_groups:
                    th_stage_groups[stage] = []
                th_stage_groups[stage].append(applicant)

            # Đếm ứng viên có template
            th_applicants_with_template = 0
            th_info_html = '<div class="th_stage_groups_info d-flex flex-column">'

            for stage, applicants in th_stage_groups.items():
                has_template = bool(stage.template_id)
                if has_template:
                    th_applicants_with_template += len(applicants)

                # Đếm ứng viên có email trong nhóm này
                th_applicants_with_email_in_group = [
                    a for a in applicants
                    if self.th_get_applicant_email(a)
                ]

                # Tạo thông tin HTML cho nhóm
                th_status_class = 'text-success' if has_template else 'text-warning'
                th_template_name = stage.template_id.name if has_template else 'Chưa cấu hình'

                th_info_html += f'''
                <div class="mb-2 p-2 border rounded">
                    <strong>{stage.name}</strong>
                    <span class="badge badge-primary">{len(applicants)} ứng viên</span>
                    <span class="badge badge-info">{len(th_applicants_with_email_in_group)} có email</span>
                    <br/>
                    <small class="{th_status_class}">
                        <i class="fa fa-envelope"></i> Template: {th_template_name}
                    </small>
                </div>
                '''

            th_info_html += '</div>'
            record.th_stage_group_info = th_info_html

    def th_get_stage_groups(self):
        """Lấy danh sách nhóm ứng viên theo giai đoạn"""
        th_stage_groups = {}
        for applicant in self.th_applicant_ids:
            stage = applicant.stage_id
            if stage not in th_stage_groups:
                th_stage_groups[stage] = {
                    'stage': stage,
                    'applicants': [],
                    'template': stage.template_id,
                    'has_template': bool(stage.template_id)
                }
            th_stage_groups[stage]['applicants'].append(applicant)
        return list(th_stage_groups.values())

    def th_validate_applicants(self):
        """Kiểm tra tính hợp lệ của danh sách ứng viên"""
        if not self.th_applicant_ids:
            raise UserError(_("Vui lòng chọn ít nhất một ứng viên để gửi email."))

        # Kiểm tra có ứng viên nào có template không
        th_stage_groups = self.th_get_stage_groups()
        th_groups_with_template = [g for g in th_stage_groups if g['has_template']]

        if not th_groups_with_template:
            raise UserError(_(
                "Không có ứng viên nào ở giai đoạn có cấu hình template email. "
                "Vui lòng cấu hình template cho các giai đoạn tuyển dụng trước."
            ))

        # Kiểm tra ứng viên có email
        th_applicants_without_email = []
        for group in th_groups_with_template:
            for applicant in group['applicants']:
                th_email = self.th_get_applicant_email(applicant)
                if not th_email:
                    th_applicants_without_email.append(applicant)

        if th_applicants_without_email:
            th_names = ', '.join([a.partner_name or a.name for a in th_applicants_without_email])
            raise UserError(_(
                "Các ứng viên sau không có địa chỉ email: %s. "
                "Vui lòng cập nhật email trước khi gửi."
            ) % th_names)

    def th_get_applicant_email(self, th_applicant):
        """Lấy địa chỉ email của ứng viên"""
        if th_applicant.email_from:
            return th_applicant.email_from
        else:
            return False


    def th_send_direct_email(self, th_template, th_applicant):
        """Gửi email trực tiếp qua SMTP tới ứng viên"""
        try:
            # Lấy địa chỉ email đích
            th_recipient_email = self.th_get_applicant_email(th_applicant)
            if not th_recipient_email:
                raise ValueError(f"Không tìm thấy email cho ứng viên {th_applicant.partner_name or th_applicant.name}")

            # Render template đúng cách bằng generate_email method
            # Đây là cách chuẩn để render mail template trong Odoo 16
            th_email_values = th_template.with_context(
                lang=th_applicant.partner_id.lang if th_applicant.partner_id else self.env.user.lang
            ).generate_email(th_applicant.id, fields=['subject', 'body_html', 'email_from', 'reply_to'])

            # Lấy subject và body đã được render với giá trị thực
            th_subject = th_email_values.get('subject', th_template.subject or '')
            th_body = th_email_values.get('body_html', th_template.body_html or '')
            th_email_from = th_email_values.get('email_from', th_template.email_from or self.env.user.email)
            th_reply_to = th_email_values.get('reply_to', th_template.reply_to or self.env.user.email)

            # Tạo mail.mail record để gửi trực tiếp
            th_mail_values = {
                'subject': th_subject,
                'body_html': th_body,
                'email_to': th_recipient_email,
                'email_from': th_email_from,
                'reply_to': th_reply_to,
                'auto_delete': True,
                'state': 'outgoing',
                'model': 'hr.applicant',
                'res_id': th_applicant.id,
            }

            # Tạo và gửi email
            th_mail = self.env['mail.mail'].create(th_mail_values)
            th_mail.send()

            # Ghi log vào chatter của ứng viên để theo dõi
            th_applicant.message_post(
                subject=f"Đã gửi email: {th_subject}",
                body=f"Email đã được gửi tới {th_recipient_email} sử dụng template '{th_template.name}'",
                message_type='comment',
                subtype_xmlid='mail.mt_note'
            )

            return True

        except Exception as e:
            # Ghi log lỗi vào chatter
            th_applicant.message_post(
                subject="Lỗi gửi email",
                body=f"Không thể gửi email tới {th_recipient_email}: {str(e)}",
                message_type='comment',
                subtype_xmlid='mail.mt_note'
            )
            raise e

    def th_send_emails_by_stage(self):
        """Gửi email theo giai đoạn với template tương ứng"""
        th_stage_groups = self.th_get_stage_groups()
        th_groups_with_template = [g for g in th_stage_groups if g['has_template']]

        th_total_sent = 0
        th_failed_applicants = []
        th_stage_results = []

        for group in th_groups_with_template:
            stage = group['stage']
            template = group['template']
            applicants = group['applicants']

            # Lọc ứng viên có email
            th_valid_applicants = [
                a for a in applicants
                if self.th_get_applicant_email(a)
            ]

            th_sent_in_group = 0
            th_failed_in_group = []

            for applicant in th_valid_applicants:
                try:
                    # Gửi email trực tiếp qua SMTP
                    self.th_send_direct_email(template, applicant)

                    th_sent_in_group += 1
                    th_total_sent += 1

                except Exception as e:
                    th_failed_in_group.append(applicant.partner_name or applicant.name)
                    th_failed_applicants.append(applicant.partner_name or applicant.name)

            # Lưu kết quả cho từng stage
            th_stage_results.append({
                'stage_name': stage.name,
                'template_name': template.name,
                'sent_count': th_sent_in_group,
                'failed_count': len(th_failed_in_group),
                'failed_names': th_failed_in_group
            })

        # Tạo thông báo kết quả chi tiết
        return self.th_create_result_notification(th_total_sent, th_failed_applicants, th_stage_results)

    def th_create_result_notification(self, th_total_sent, th_failed_applicants, th_stage_results):
        """Tạo thông báo kết quả gửi email"""
        if not th_failed_applicants:
            # Thành công hoàn toàn
            th_message_parts = [f"Đã gửi email thành công cho {th_total_sent} ứng viên"]

            # Chi tiết theo stage
            for result in th_stage_results:
                th_message_parts.append(
                    f"• {result['stage_name']}: {result['sent_count']} email ({result['template_name']})"
                )

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'type': 'success',
                    'message':'\n'.join(th_message_parts),
                    'sticky': True,
                }
            }
        else:
            # Có lỗi
            th_message_parts = [
                f"Đã gửi email cho {th_total_sent} ứng viên",
                f"Không thể gửi cho {len(th_failed_applicants)} ứng viên: {', '.join(th_failed_applicants)}"
            ]

            # Chi tiết theo stage
            for result in th_stage_results:
                if result['sent_count'] > 0:
                    th_message_parts.append(
                        f"• {result['stage_name']}: {result['sent_count']} thành công"
                    )
                if result['failed_count'] > 0:
                    th_message_parts.append(
                        f"• {result['stage_name']}: {result['failed_count']} thất bại"
                    )

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'type': 'warning',
                    'message': '\n'.join(th_message_parts),
                    'sticky': True,
                }
            }

    def action_send_email(self):
        """Action chính để gửi email hoàng loạt theo giai đoạn"""
        self.ensure_one()

        # Validate dữ liệu đầu vào
        self.th_validate_applicants()

        # Hiển thị confirmation dialog nếu gửi cho nhiều ứng viên
        if len(self.th_applicant_ids) > 5:
            return {
                'type': 'ir.actions.act_window',
                'name': _('Xác nhận gửi email'),
                'res_model': 'th.applicant.send.mail.confirm',
                'view_mode': 'form',
                'target': 'new',
                'context': {
                    'default_wizard_id': self.id,
                    'default_applicant_count': len(self.th_applicant_ids),
                },
                'reload': True,
            }

        # Gửi email theo giai đoạn
        return self.th_send_emails_by_stage()

    def th_force_send_email(self):
        """Gửi email mà không cần confirmation"""
        return self.th_send_emails_by_stage()


class ThApplicantSendMailConfirm(models.TransientModel):
    _name = 'th.applicant.send.mail.confirm'
    _description = 'Xác nhận gửi email hàng loạt'

    wizard_id = fields.Many2one('th.applicant.send.mail', string='Wizard', required=True)
    applicant_count = fields.Integer(string='Số lượng ứng viên', required=True)

    def action_confirm_send(self):
        """Xác nhận và gửi email"""
        self.ensure_one()
        return self.wizard_id.th_force_send_email()

    def action_cancel(self):
        """Hủy gửi email"""
        return {'type': 'ir.actions.act_window_close'}
