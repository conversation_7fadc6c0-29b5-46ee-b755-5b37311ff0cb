<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Form view cho wizard gửi email hàng loạt -->
        <record id="th_applicant_send_mail_view_form" model="ir.ui.view">
            <field name="name">th.applicant.send.mail.form</field>
            <field name="model">th.applicant.send.mail</field>
            <field name="arch" type="xml">
                <form string="Gửi email hàng loạt cho ứng viên">
                    <field name="th_author_id" invisible="1"/>

                    <div class="alert alert-info" role="alert">
                        <strong>Hướng dẫn:</strong> Chọn mẫu email có sẵn hoặc soạn email tùy chỉnh để gửi cho các ứng viên đã chọn.
                        <br/>
                        <small>
                            <i class="fa fa-lightbulb-o"/>
                            <strong>Mẹo:</strong> Khi chọn mẫu email, nội dung sẽ tự động điền thông tin của ứng viên đầu tiên để preview.
                        </small>
                    </div>

                    <group>
                        <group string="Cài đặt email">
                            <field name="th_use_template" widget="boolean_toggle"/>
                            <field name="th_template_id"
                                   attrs="{'invisible': [('th_use_template', '=', False)], 'required': [('th_use_template', '=', True)]}"
                                   options="{'no_create': True, 'no_edit': True}"/>
                        </group>
                        <group string="Thông tin email">
                            <field name="subject" required="1" placeholder="Nhập tiêu đề email..."/>
                        </group>
                    </group>
                    
                    <group string="Danh sách ứng viên">
                        <field name="th_applicant_ids"
                               widget="many2many_tags"
                               context="{'show_partner_name': 1}"
                               options="{'color_field': 'color', 'no_create': True}"/>
                        <div class="row">
                            <div class="col-md-6">
                                <field name="th_applicant_count" readonly="1"/>
                            </div>
                            <div class="col-md-6">
                                <field name="th_applicant_with_email_count" readonly="1"/>
                            </div>
                        </div>
                    </group>
                    
                    <group string="Nội dung email">
                        <field name="body" 
                               nolabel="1" 
                               class="oe-bordered-editor"
                               placeholder="Nhập nội dung email hoặc chọn mẫu email có sẵn..."
                               options="{'style-inline': true}" 
                               force_save="1"/>
                    </group>
                    
                    <footer>
                        <button name="action_send_email" 
                                string="Gửi email" 
                                type="object" 
                                class="btn-primary" 
                                data-hotkey="q"/>
                        <button string="Hủy" 
                                class="btn-secondary" 
                                special="cancel" 
                                data-hotkey="z"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Action window cho wizard -->
        <record id="th_applicant_send_mail_action" model="ir.actions.act_window">
            <field name="name">Gửi email hàng loạt</field>
            <field name="res_model">th.applicant.send.mail</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="context">{
                'default_th_applicant_ids': active_ids,
                'default_th_use_template': True
            }</field>
        </record>

        <!-- Inherit form view của hr.applicant để thêm button gửi email -->
        <record id="th_hr_applicant_view_form_inherit" model="ir.ui.view">
            <field name="name">hr.applicant.form.inherit</field>
            <field name="model">hr.applicant</field>
            <field name="inherit_id" ref="hr_recruitment.hr_applicant_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//header" position="inside">
                    <button name="th_action_send_email_with_template"
                            string="Gửi email với template"
                            type="object"
                            class="btn-secondary"
                            groups="hr_recruitment.group_hr_recruitment_user"/>
                </xpath>
            </field>
        </record>

        <!-- Confirmation wizard form view -->
        <record id="th_applicant_send_mail_confirm_view_form" model="ir.ui.view">
            <field name="name">th.applicant.send.mail.confirm.form</field>
            <field name="model">th.applicant.send.mail.confirm</field>
            <field name="arch" type="xml">
                <form string="Xác nhận gửi email hàng loạt">
                    <field name="wizard_id" invisible="1"/>

                    <div class="alert alert-warning" role="alert">
                        <h4><i class="fa fa-warning"/> Xác nhận gửi email hàng loạt</h4>
                        <p>
                            Bạn đang chuẩn bị gửi email cho <strong><field name="applicant_count" readonly="1"/> ứng viên</strong>.
                        </p>
                        <p>
                            Việc gửi email hàng loạt có thể mất một thời gian. Bạn có chắc chắn muốn tiếp tục?
                        </p>
                    </div>

                    <footer>
                        <button name="action_confirm_send"
                                string="Xác nhận gửi"
                                type="object"
                                class="btn-primary"/>
                        <button name="action_cancel"
                                string="Hủy"
                                type="object"
                                class="btn-secondary"/>
                    </footer>
                </form>
            </field>
        </record>

    </data>
</odoo>
