# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class ThHrApplicant(models.Model):
    _inherit = 'hr.applicant'

    def th_action_send_email_with_template(self):
        """Mở wizard gửi email với template cho ứng viên hiện tạ<PERSON>"""
        self.ensure_one()
        return {
            'name': _('Gửi email với template'),
            'type': 'ir.actions.act_window',
            'res_model': 'th.applicant.send.mail',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_th_applicant_ids': self.ids,
                'default_th_use_template': True,
            }
        }

    def th_action_send_bulk_email(self):
        """Mở wizard gửi email hàng loạt cho nhiều ứng viên"""
        return {
            'name': _('Gửi email hàng loạt'),
            'type': 'ir.actions.act_window',
            'res_model': 'th.applicant.send.mail',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_th_applicant_ids': self.ids,
                'default_th_use_template': True,
            }
        }
